<?php

/**
 * Generate a 6-digit numeric verification code.
 */
function generateVerificationCode(): string {
    return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Send a verification code to an email.
 */
function sendVerificationEmail(string $email, string $code): bool {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['verification_code_' . md5($email)] = $code;
    
    $subject = "Your Verification Code";
    $body = "<p>Your verification code is: <strong>$code</strong></p>";
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    
    ini_set('SMTP', 'localhost');
    ini_set('smtp_port', '1025');
    ini_set('sendmail_from', '<EMAIL>');
    
    return mail($email, $subject, $body, $headers);
}

/**
 * Register an email by storing it in a file.
 */
function registerEmail(string $email): bool {
  $file = __DIR__ . '/registered_emails.txt';
    if (!file_exists($file)) {
        touch($file);
    }
    
    $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (!in_array($email, $emails)) {
        return file_put_contents($file, $email . PHP_EOL, FILE_APPEND | LOCK_EX) !== false;
    }
    return true;
}

/**
 * Unsubscribe an email by removing it from the list.
 */
function unsubscribeEmail(string $email): bool {
  $file = __DIR__ . '/registered_emails.txt';
    if (!file_exists($file)) {
        return false;
    }
    
    $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $filteredEmails = array_filter($emails, function($registeredEmail) use ($email) {
        return $registeredEmail !== $email;
    });
    
    return file_put_contents($file, implode(PHP_EOL, $filteredEmails) . (count($filteredEmails) > 0 ? PHP_EOL : ''), LOCK_EX) !== false;
}

/**
 * Fetch random XKCD comic and format data as HTML.
 */
function fetchAndFormatXKCDData(): string {
    $latestComicUrl = 'https://xkcd.com/info.0.json';
    $latestComicData = file_get_contents($latestComicUrl);
    $latestComic = json_decode($latestComicData, true);
    
    $randomComicId = random_int(1, $latestComic['num']);
    $randomComicUrl = "https://xkcd.com/$randomComicId/info.0.json";
    
    $comicData = file_get_contents($randomComicUrl);
    $comic = json_decode($comicData, true);
    
    return '<h2>XKCD Comic</h2>
<img src="' . $comic['img'] . '" alt="XKCD Comic">
<p><a href="#" id="unsubscribe-button">Unsubscribe</a></p>';
}

/**
 * Send the formatted XKCD updates to registered emails.
 */
function sendXKCDUpdatesToSubscribers(): void {
  $file = __DIR__ . '/registered_emails.txt';
    if (!file_exists($file)) {
        return;
    }
    
    $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $xkcdContent = fetchAndFormatXKCDData();
    
    $subject = "Your XKCD Comic";
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    
    ini_set('SMTP', 'localhost');
    ini_set('smtp_port', '1025');
    ini_set('sendmail_from', '<EMAIL>');
    
    foreach ($emails as $email) {
        mail($email, $subject, $xkcdContent, $headers);
    }
}

/**
 * Verify if the provided code matches the sent verification code.
 */
function verifyCode(string $email, string $code): bool {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $sessionKey = 'verification_code_' . md5($email);
    
    if (isset($_SESSION[$sessionKey]) && $_SESSION[$sessionKey] === $code) {
        unset($_SESSION[$sessionKey]);
        return true;
    }
    
    return false;
}

/**
 * Send unsubscribe confirmation email with verification code.
 */
function sendUnsubscribeEmail(string $email, string $code): bool {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['unsubscribe_code_' . md5($email)] = $code;
    
    $subject = "Confirm Un-subscription";
    $body = "<p>To confirm un-subscription, use this code: <strong>$code</strong></p>";
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    
    ini_set('SMTP', 'localhost');
    ini_set('smtp_port', '1025');
    ini_set('sendmail_from', '<EMAIL>');
    
    return mail($email, $subject, $body, $headers);
}

/**
 * Verify unsubscribe code for email.
 */
function verifyUnsubscribeCode(string $email, string $code): bool {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $sessionKey = 'unsubscribe_code_' . md5($email);
    
    if (isset($_SESSION[$sessionKey]) && $_SESSION[$sessionKey] === $code) {
        unset($_SESSION[$sessionKey]);
        return true;
    }
    
    return false;
}
