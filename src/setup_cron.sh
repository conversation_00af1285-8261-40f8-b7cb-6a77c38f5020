#!/bin/bash

echo "Starting cron job setup..."

# Get the absolute path to the cron.php file
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_SCRIPT="$SCRIPT_DIR/cron.php"

echo "Script directory: $SCRIPT_DIR"
echo "Cron script path: $CRON_SCRIPT"

# Find PHP executable
PHP_PATH=""
for path in /usr/bin/php /usr/local/bin/php /opt/php/bin/php $(which php 2>/dev/null); do
    if [ -x "$path" ]; then
        PHP_PATH="$path"
        break
    fi
done

if [ -z "$PHP_PATH" ]; then
    echo "Error: PHP executable not found. Please install PHP or ensure it's in your PATH."
    echo "Note: You can still manually set up the cron job using your PHP path."
    echo "Example cron entry: 0 0 * * * /path/to/php $CRON_SCRIPT >> /tmp/xkcd_cron.log 2>&1"
    exit 1
fi

echo "PHP found at: $PHP_PATH"

# Check if the cron.php file exists
if [ ! -f "$CRON_SCRIPT" ]; then
    echo "Error: cron.php file not found at $CRON_SCRIPT"
    exit 1
fi

echo "cron.php file found"

# Create the cron job entry
CRON_ENTRY="0 0 * * * $PHP_PATH $CRON_SCRIPT >> /tmp/xkcd_cron.log 2>&1"

echo "Cron entry to add: $CRON_ENTRY"

# Check if the cron job already exists
if crontab -l 2>/dev/null | grep -q "$CRON_SCRIPT"; then
    echo "Cron job already exists for $CRON_SCRIPT"
    crontab -l 2>/dev/null | grep "$CRON_SCRIPT"
    exit 0
fi

echo "Adding cron job..."

# Add the cron job
(crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -

# Verify the cron job was added
if crontab -l 2>/dev/null | grep -q "$CRON_SCRIPT"; then
    echo "Cron job successfully added!"
    echo "The XKCD comic will be sent to subscribers daily at midnight."
    echo "Cron job: $CRON_ENTRY"
    echo "Log file: /tmp/xkcd_cron.log"
    echo ""
    echo "Current crontab:"
    crontab -l 2>/dev/null
else
    echo "Error: Failed to add cron job"
    echo "You can manually add the following line to your crontab:"
    echo "$CRON_ENTRY"
    exit 1
fi
