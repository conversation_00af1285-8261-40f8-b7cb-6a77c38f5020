#!/bin/bash

echo "=== XKCD Email System - Development Startup Script ==="
echo ""

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to start Mailpit
start_mailpit() {
    echo "Starting Mailpit..."
    if check_port 1025; then
        echo "✅ Mailpit SMTP server already running on port 1025"
    else
        echo "Starting Mailpit in background..."
        nohup mailpit > /tmp/mailpit.log 2>&1 &
        sleep 2
        if check_port 1025; then
            echo "✅ Mailpit started successfully"
        else
            echo "❌ Failed to start Mailpit"
            echo "Check if Mailpit is installed: mailpit --version"
            exit 1
        fi
    fi
    
    if check_port 8025; then
        echo "✅ Mailpit web interface available at: http://localhost:8025"
    else
        echo "❌ Mailpit web interface not available"
    fi
}

# Function to start PHP server
start_php_server() {
    echo ""
    echo "Starting PHP development server..."
    if check_port 8000; then
        echo "✅ PHP server already running on port 8000"
    else
        echo "Starting PHP server in background..."
        nohup php -S localhost:8000 > /tmp/php_server.log 2>&1 &
        sleep 2
        if check_port 8000; then
            echo "✅ PHP server started successfully"
        else
            echo "❌ Failed to start PHP server"
            exit 1
        fi
    fi
    echo "✅ XKCD application available at: http://localhost:8000"
}

# Function to run connection test
run_connection_test() {
    echo ""
    echo "Running connection test..."
    php test_mailpit_connection.php
}

# Main execution
echo "1. Starting Mailpit email server..."
start_mailpit

echo ""
echo "2. Starting PHP development server..."
start_php_server

echo ""
echo "3. Testing connections..."
run_connection_test

echo ""
echo "=== Development Environment Ready! ==="
echo ""
echo "🌐 XKCD Application: http://localhost:8000"
echo "📧 Mailpit Interface: http://localhost:8025"
echo "📁 Unsubscribe Page: http://localhost:8000/unsubscribe.php"
echo ""
echo "📋 Quick Test Steps:"
echo "1. Go to http://localhost:8000"
echo "2. Enter an email address and click Submit"
echo "3. Check http://localhost:8025 for the verification email"
echo "4. Copy the code and complete verification"
echo "5. Test XKCD delivery: php cron.php"
echo ""
echo "🛑 To stop services:"
echo "   killall mailpit"
echo "   killall php"
echo ""
echo "📖 For detailed instructions, see SETUP.md"
