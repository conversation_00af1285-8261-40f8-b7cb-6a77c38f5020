<?php
/**
 * Test script to verify Mailpit connection and email functionality
 * Run this script to ensure emails are being sent correctly through Mailpit
 */

echo "=== XKCD Email System - Mailpit Connection Test ===\n\n";

// Test 1: Check if we can connect to Mailpit SMTP
echo "1. Testing Mailpit SMTP connection (localhost:1025)...\n";

$smtp_host = 'localhost';
$smtp_port = 1025;

$connection = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 5);
if ($connection) {
    echo "✅ Mailpit SMTP server is reachable on port 1025\n";
    fclose($connection);
} else {
    echo "❌ Cannot connect to Mailpit SMTP server\n";
    echo "   Error: $errstr ($errno)\n";
    echo "   Make sure Mailpit is running: mailpit\n\n";
    exit(1);
}

// Test 2: Check if Mailpit web interface is accessible
echo "\n2. Testing Mailpit web interface (localhost:8025)...\n";

$web_connection = @fsockopen('localhost', 8025, $errno, $errstr, 5);
if ($web_connection) {
    echo "✅ Mailpit web interface is reachable on port 8025\n";
    echo "   Access it at: http://localhost:8025\n";
    fclose($web_connection);
} else {
    echo "❌ Cannot connect to Mailpit web interface\n";
    echo "   Error: $errstr ($errno)\n";
}

// Test 3: Send a test email
echo "\n3. Sending test email through Mailpit...\n";

require_once 'functions.php';

$to = '<EMAIL>';
$subject = 'XKCD System Test Email';
$message = '<h2>Test Email</h2><p>This is a test email from the XKCD system to verify Mailpit integration.</p>';
$headers = "From: <EMAIL>\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";
$headers .= "MIME-Version: 1.0\r\n";

$result = sendEmailViaSMTP($to, $subject, $message, $headers);

if ($result) {
    echo "✅ Test email sent successfully!\n";
    echo "   Check Mailpit web interface: http://localhost:8025\n";
    echo "   You should see an email with subject: '$subject'\n";
} else {
    echo "❌ Failed to send test email\n";
    echo "   Check PHP error logs for more details\n";
}

// Test 4: Test the XKCD functions
echo "\n4. Testing XKCD application functions...\n";

// Test verification code generation
$code = generateVerificationCode();
if (strlen($code) === 6 && is_numeric($code)) {
    echo "✅ Verification code generation works: $code\n";
} else {
    echo "❌ Verification code generation failed\n";
}

// Test XKCD fetching
try {
    $xkcdContent = fetchAndFormatXKCDData();
    if (strpos($xkcdContent, '<h2>XKCD Comic</h2>') !== false) {
        echo "✅ XKCD comic fetching works\n";
    } else {
        echo "❌ XKCD comic fetching failed\n";
    }
} catch (Exception $e) {
    echo "❌ XKCD comic fetching error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "If all tests passed, your system is ready!\n";
echo "Next steps:\n";
echo "1. Start PHP server: php -S localhost:8000\n";
echo "2. Open browser: http://localhost:8000\n";
echo "3. Test email registration workflow\n";
echo "4. Check emails in Mailpit: http://localhost:8025\n\n";

echo "If any tests failed, check the troubleshooting section in SETUP.md\n";
