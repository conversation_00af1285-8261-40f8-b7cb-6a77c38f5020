<?php
require_once 'functions.php';

$message = '';

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsubscribe_email']) && !isset($_POST['verification_code'])) {
        $email = filter_var($_POST['unsubscribe_email'], FILTER_VALIDATE_EMAIL);
        if ($email) {
            $code = generateVerificationCode();
            if (sendUnsubscribeEmail($email, $code)) {
                $message = 'Unsubscribe verification code sent to your email!';
            } else {
                $message = 'Failed to send unsubscribe verification email.';
            }
        } else {
            $message = 'Please enter a valid email address.';
        }
    } elseif (isset($_POST['verification_code']) && isset($_POST['email_for_unsubscribe'])) {
        $email = $_POST['email_for_unsubscribe'];
        $code = $_POST['verification_code'];
        
        if (verifyUnsubscribeCode($email, $code)) {
            if (unsubscribeEmail($email)) {
                $message = 'Email successfully unsubscribed from XKCD comics!';
            } else {
                $message = 'Unsubscription failed. Please try again.';
            }
        } else {
            $message = 'Invalid verification code. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe from XKCD Comics</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        input[type="email"], input[type="text"] { width: 100%; padding: 10px; margin: 10px 0; box-sizing: border-box; }
        button { background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #c82333; }
        .message { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Unsubscribe from XKCD Comics</h1>
    
    <?php if ($message): ?>
        <div class="message <?php echo (strpos($message, 'success') !== false || strpos($message, 'sent') !== false || strpos($message, 'unsubscribed') !== false) ? 'success' : 'error'; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <div class="form-section">
        <h2>Step 1: Enter Your Email</h2>
        <form method="POST">
            <input type="email" name="unsubscribe_email" placeholder="Enter your email address" required>
            <button type="submit" id="submit-unsubscribe">Unsubscribe</button>
        </form>
    </div>

    <div class="form-section">
        <h2>Step 2: Enter Verification Code</h2>
        <form method="POST">
            <input type="email" name="email_for_unsubscribe" placeholder="Enter your email address again" required>
            <input type="text" name="verification_code" maxlength="6" placeholder="Enter 6-digit verification code" required>
            <button type="submit" id="submit-verification">Verify</button>
        </form>
    </div>

    <p><a href="index.php">Back to subscription page</a></p>
</body>
</html>
